import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Material modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';

// Services
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

// Components
import { CategoryWorkareaMappingComponent, CategoryWorkareaMapping, WorkAreaData } from './category-workarea-mapping.component';

@Component({
  selector: 'app-category-workarea-demo',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDividerModule,
    MatSnackBarModule,
    CategoryWorkareaMappingComponent
  ],
  template: `
    <div class="demo-container">
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>category</mat-icon>
            Category to Work Area Mapping Demo
          </mat-card-title>
          <mat-card-subtitle>
            Configure which work areas are associated with each category
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <div class="demo-layout">
            <!-- Left Side - Category Selection -->
            <div class="left-panel">
              <h3>
                <mat-icon>list</mat-icon>
                Select Categories
              </h3>
              
              <mat-form-field appearance="outline" class="category-selector">
                <mat-label>Categories</mat-label>
                <mat-select 
                  [formControl]="selectedCategoriesCtrl" 
                  multiple
                  placeholder="Select categories to configure"
                >
                  <mat-option *ngFor="let category of categories" [value]="category">
                    {{ category }}
                  </mat-option>
                </mat-select>
                <mat-hint>Select categories to configure their work area mappings</mat-hint>
              </mat-form-field>

              <div class="selected-info" *ngIf="selectedCategoriesCtrl.value?.length > 0">
                <p><strong>Selected Categories:</strong></p>
                <ul>
                  <li *ngFor="let category of selectedCategoriesCtrl.value">{{ category }}</li>
                </ul>
              </div>
            </div>

            <!-- Right Side - Category-WorkArea Mapping -->
            <div class="right-panel">
              <h3>
                <mat-icon>settings</mat-icon>
                Configure Work Area Mappings
              </h3>
              
              <app-category-workarea-mapping
                [categories]="categories"
                [selectedCategoryNames]="selectedCategoriesCtrl.value || []"
                [workAreas]="workAreas"
                [existingMappings]="existingMappings"
                [tenantId]="tenantId"
                [showAsDialog]="false"
                (mappingsChanged)="onMappingsChanged($event)"
              ></app-category-workarea-mapping>
            </div>
          </div>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button (click)="resetDemo()">
            <mat-icon>refresh</mat-icon>
            Reset Demo
          </button>
          <button mat-raised-button color="primary" (click)="showCurrentMappings()" [disabled]="currentMappings.length === 0">
            <mat-icon>visibility</mat-icon>
            Show Current Mappings
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Current Mappings Display -->
      <mat-card class="mappings-display" *ngIf="showMappings && currentMappings.length > 0">
        <mat-card-header>
          <mat-card-title>Current Category-WorkArea Mappings</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="mappings-grid">
            <div *ngFor="let mapping of currentMappings" class="mapping-item">
              <div class="mapping-category">
                <mat-icon>category</mat-icon>
                <strong>{{ mapping.categoryName }}</strong>
              </div>
              <div class="mapping-workareas">
                <mat-icon>business</mat-icon>
                <span>{{ mapping.workAreas.join(', ') }}</span>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .demo-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .demo-card {
      margin-bottom: 20px;
    }

    .demo-layout {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: 24px;
      margin-top: 16px;
    }

    .left-panel, .right-panel {
      h3 {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 16px 0;
        color: #333;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .left-panel {
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 8px;
      border-left: 4px solid #ff9800;

      .category-selector {
        width: 100%;
        margin-bottom: 16px;
      }

      .selected-info {
        p {
          margin: 0 0 8px 0;
          font-weight: 500;
          color: #333;
        }

        ul {
          margin: 0;
          padding-left: 20px;
          
          li {
            margin-bottom: 4px;
            color: #666;
          }
        }
      }
    }

    .right-panel {
      min-height: 400px;
    }

    .mappings-display {
      .mappings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;

        .mapping-item {
          padding: 16px;
          background-color: #f9f9f9;
          border-radius: 8px;
          border-left: 3px solid #ff9800;

          .mapping-category {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;

            mat-icon {
              color: #ff9800;
              font-size: 18px;
              width: 18px;
              height: 18px;
            }
          }

          .mapping-workareas {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #666;

            mat-icon {
              color: #666;
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }

    @media (max-width: 768px) {
      .demo-layout {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class CategoryWorkareaDemoComponent implements OnInit, OnDestroy {
  // Form controls
  selectedCategoriesCtrl = new FormControl<string[]>([]);

  // Data
  categories: string[] = ['Food', 'Beverages', 'Tobacco', 'Liquor', 'Desserts', 'Snacks'];
  workAreas: WorkAreaData[] = [
    {
      restaurantIdOld: 'INDIAN-KITCHEN',
      branchName: 'Indian Kitchen',
      workAreas: ['Kitchen', 'Bar', 'Bakery'],
      disabled: false
    },
    {
      restaurantIdOld: 'MAIN-BRANCH',
      branchName: 'Main Branch',
      workAreas: ['Kitchen', 'Grill', 'Juice Station'],
      disabled: false
    }
  ];

  existingMappings: CategoryWorkareaMapping[] = [
    {
      categoryName: 'Food',
      workAreas: ['Kitchen']
    },
    {
      categoryName: 'Beverages',
      workAreas: ['Bar', 'Juice Station']
    }
  ];

  currentMappings: CategoryWorkareaMapping[] = [];
  showMappings = false;
  tenantId = 'demo-tenant';

  // Destroy subject for cleanup
  private destroy$ = new Subject<void>();

  constructor(
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Initialize with existing mappings
    this.currentMappings = [...this.existingMappings];

    // Set initial category selection to show the existing mappings
    const initialCategories = this.existingMappings.map(m => m.categoryName);
    this.selectedCategoriesCtrl.setValue(initialCategories);

    // Listen to category selection changes
    this.selectedCategoriesCtrl.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(selectedCategories => {
        console.log('Selected categories changed:', selectedCategories);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onMappingsChanged(mappings: CategoryWorkareaMapping[]): void {
    this.currentMappings = mappings;
    this.showSuccess(`Updated ${mappings.length} category-workarea mappings`);
    console.log('Mappings updated:', mappings);
  }

  showCurrentMappings(): void {
    this.showMappings = !this.showMappings;
  }

  resetDemo(): void {
    this.selectedCategoriesCtrl.setValue([]);
    this.currentMappings = [...this.existingMappings];
    this.showMappings = false;
    this.showSuccess('Demo reset to initial state');
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }
}
