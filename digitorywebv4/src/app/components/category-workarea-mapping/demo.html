<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Category to Work Area Mapping - Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
        }
        
        .demo-layout {
            display: grid;
            grid-template-columns: 1fr 2fr;
            min-height: 600px;
        }
        
        .left-panel {
            background: #f8f9fa;
            padding: 20px;
            border-right: 1px solid #e0e0e0;
        }
        
        .right-panel {
            padding: 20px;
        }
        
        .section-title {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .icon {
            width: 20px;
            height: 20px;
            fill: #ff9800;
        }
        
        .category-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .category-item {
            display: flex;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #ff9800;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .category-item:hover {
            transform: translateX(4px);
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
        }
        
        .category-item.selected {
            background: #fff3e0;
            border-left-color: #f57c00;
        }
        
        .category-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            fill: #ff9800;
        }
        
        .mapping-area {
            min-height: 400px;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: #666;
            text-align: center;
        }
        
        .empty-icon {
            width: 48px;
            height: 48px;
            fill: #ccc;
            margin-bottom: 16px;
        }
        
        .mapping-row {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            margin-bottom: 12px;
            background: #f9f9f9;
            border-radius: 8px;
            border-left: 4px solid #ff9800;
        }
        
        .category-name {
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 150px;
            font-weight: 500;
            color: #333;
        }
        
        .workarea-selector {
            flex: 1;
        }
        
        .workarea-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .workarea-tag {
            background: #ff9800;
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .workarea-dropdown {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }
        
        .summary-section {
            margin-top: 24px;
            padding: 16px;
            background: #f0f7ff;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        .summary-title {
            margin: 0 0 12px 0;
            color: #1976d2;
            font-weight: 600;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
        }
        
        .summary-item {
            background: white;
            padding: 12px;
            border-radius: 6px;
            border-left: 3px solid #ff9800;
        }
        
        .summary-category {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .summary-workareas {
            font-size: 13px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .demo-layout {
                grid-template-columns: 1fr;
            }
            
            .left-panel {
                border-right: none;
                border-bottom: 1px solid #e0e0e0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Category to Work Area Mapping</h1>
            <p>Configure which work areas are associated with each inventory category</p>
        </div>
        
        <div class="demo-layout">
            <!-- Left Panel - Categories -->
            <div class="left-panel">
                <h2 class="section-title">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                    </svg>
                    Select Categories
                </h2>
                
                <ul class="category-list" id="categoryList">
                    <li class="category-item" data-category="Food">
                        <svg class="category-icon" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                        </svg>
                        Food
                    </li>
                    <li class="category-item" data-category="Beverages">
                        <svg class="category-icon" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                        </svg>
                        Beverages
                    </li>
                    <li class="category-item" data-category="Tobacco">
                        <svg class="category-icon" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                        </svg>
                        Tobacco
                    </li>
                    <li class="category-item" data-category="Liquor">
                        <svg class="category-icon" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                        </svg>
                        Liquor
                    </li>
                </ul>
            </div>
            
            <!-- Right Panel - Work Area Mapping -->
            <div class="right-panel">
                <h2 class="section-title">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                    </svg>
                    Configure Work Area Mappings
                </h2>
                
                <div class="mapping-area" id="mappingArea">
                    <div class="empty-state">
                        <svg class="empty-icon" viewBox="0 0 24 24">
                            <path d="M15,4V6H18V18A2,2 0 0,1 16,20H8A2,2 0 0,1 6,18V6H9V4H15M10,6V18H14V6H10Z"/>
                        </svg>
                        <p>Select categories from the left to configure work area mappings</p>
                    </div>
                </div>
                
                <div class="summary-section" id="summarySection" style="display: none;">
                    <h3 class="summary-title">Current Mappings Summary</h3>
                    <div class="summary-grid" id="summaryGrid">
                        <!-- Summary items will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Available work areas
        const workAreas = ['Kitchen', 'Bar', 'Bakery', 'Grill', 'Juice Station'];
        
        // Current mappings
        let currentMappings = {};
        
        // Selected categories
        let selectedCategories = [];
        
        // DOM elements
        const categoryList = document.getElementById('categoryList');
        const mappingArea = document.getElementById('mappingArea');
        const summarySection = document.getElementById('summarySection');
        const summaryGrid = document.getElementById('summaryGrid');
        
        // Add click event listeners to category items
        categoryList.addEventListener('click', function(e) {
            const categoryItem = e.target.closest('.category-item');
            if (categoryItem) {
                const category = categoryItem.dataset.category;
                toggleCategory(category, categoryItem);
            }
        });
        
        function toggleCategory(category, element) {
            if (selectedCategories.includes(category)) {
                // Remove category
                selectedCategories = selectedCategories.filter(c => c !== category);
                element.classList.remove('selected');
            } else {
                // Add category
                selectedCategories.push(category);
                element.classList.add('selected');
            }
            
            updateMappingArea();
            updateSummary();
        }
        
        function updateMappingArea() {
            if (selectedCategories.length === 0) {
                mappingArea.innerHTML = `
                    <div class="empty-state">
                        <svg class="empty-icon" viewBox="0 0 24 24">
                            <path d="M15,4V6H18V18A2,2 0 0,1 16,20H8A2,2 0 0,1 6,18V6H9V4H15M10,6V18H14V6H10Z"/>
                        </svg>
                        <p>Select categories from the left to configure work area mappings</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            selectedCategories.forEach(category => {
                const selectedWorkAreas = currentMappings[category] || [];
                const workAreaOptions = workAreas.map(wa => 
                    `<option value="${wa}" ${selectedWorkAreas.includes(wa) ? 'selected' : ''}>${wa}</option>`
                ).join('');
                
                html += `
                    <div class="mapping-row">
                        <div class="category-name">
                            <svg class="category-icon" viewBox="0 0 24 24">
                                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                            </svg>
                            ${category}
                        </div>
                        <div class="workarea-selector">
                            <select class="workarea-dropdown" multiple data-category="${category}" onchange="updateMapping('${category}', this)">
                                ${workAreaOptions}
                            </select>
                        </div>
                    </div>
                `;
            });
            
            mappingArea.innerHTML = html;
        }
        
        function updateMapping(category, selectElement) {
            const selectedOptions = Array.from(selectElement.selectedOptions).map(option => option.value);
            currentMappings[category] = selectedOptions;
            updateSummary();
        }
        
        function updateSummary() {
            const hasAnyMappings = Object.keys(currentMappings).some(category => 
                currentMappings[category] && currentMappings[category].length > 0
            );
            
            if (!hasAnyMappings) {
                summarySection.style.display = 'none';
                return;
            }
            
            summarySection.style.display = 'block';
            
            let html = '';
            Object.keys(currentMappings).forEach(category => {
                const workAreas = currentMappings[category];
                if (workAreas && workAreas.length > 0) {
                    html += `
                        <div class="summary-item">
                            <div class="summary-category">${category}</div>
                            <div class="summary-workareas">${workAreas.length} work areas: ${workAreas.join(', ')}</div>
                        </div>
                    `;
                }
            });
            
            summaryGrid.innerHTML = html;
        }
        
        // Initialize with some demo data
        setTimeout(() => {
            // Auto-select Food category to show the interface
            const foodItem = document.querySelector('[data-category="Food"]');
            if (foodItem) {
                toggleCategory('Food', foodItem);
                // Set some default mappings
                currentMappings['Food'] = ['Kitchen'];
                updateMappingArea();
            }
        }, 500);
    </script>
</body>
</html>
