<div class="category-mapping-container" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    <div class="mapping-layout">
      <!-- Left Panel: Category Selection -->
      <div class="left-panel">
        <div class="panel-header">
          <mat-icon>category</mat-icon>
          <span>Select Category</span>
        </div>

        <mat-form-field appearance="outline" class="category-select-field">
          <mat-select
            [value]="selectedCategoryForMapping"
            (selectionChange)="onCategorySelectionChange($event.value)"
            placeholder="Select category (0/{{ availableCategoriesForMapping.length }})"
          >
            <mat-option
              *ngFor="let category of availableCategoriesForMapping; trackBy: trackByCategory"
              [value]="category"
            >
              {{ category }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Right Panel: Work Area Configuration -->
      <div class="right-panel">
        <div class="panel-header">
          <mat-icon>work</mat-icon>
          <span>Configure Work Area Mappings</span>
        </div>

        <div class="workarea-content" *ngIf="selectedCategoryForMapping; else selectCategoryPrompt">
          <div class="selected-category-info">
            <strong>{{ selectedCategoryForMapping }}</strong>
          </div>

          <mat-form-field appearance="outline" class="workarea-field">
            <mat-select
              [value]="getCurrentWorkAreasForCategory(selectedCategoryForMapping)"
              (selectionChange)="onWorkAreasSelectionChange($event.value)"
              multiple
              placeholder="Select work areas"
            >
              <mat-option
                *ngFor="let workArea of getAvailableWorkAreasForCategory(selectedCategoryForMapping); trackBy: trackByWorkArea"
                [value]="workArea"
              >
                {{ workArea }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Current Mappings Summary -->
          <div class="mappings-summary" *ngIf="mappings.length > 0">
            <h4>Configured Categories ({{ mappings.length }})</h4>
            <div class="mapping-chips">
              <mat-chip-listbox>
                <mat-chip
                  *ngFor="let mapping of mappings"
                  [class.selected-chip]="mapping.categoryName === selectedCategoryForMapping"
                  (click)="selectCategoryFromChip(mapping.categoryName)"
                >
                  {{ mapping.categoryName }} ({{ mapping.workAreas.length }})
                  <button
                    matChipRemove
                    (click)="removeCategoryMapping(mapping.categoryName); $event.stopPropagation()"
                  >
                    <mat-icon>cancel</mat-icon>
                  </button>
                </mat-chip>
              </mat-chip-listbox>
            </div>
          </div>
        </div>

        <ng-template #selectCategoryPrompt>
          <div class="empty-state">
            <mat-icon class="empty-icon">arrow_back</mat-icon>
            <p>Select category from the left to configure work area mappings</p>
          </div>
        </ng-template>
      </div>
    </div>
  </div>

  <!-- Actions -->
  <div class="mapping-actions">
    <button
      mat-button
      type="button"
      (click)="onClose()"
      *ngIf="showAsDialog"
      class="cancel-button"
    >
      Cancel
    </button>

    <button
      mat-raised-button
      color="primary"
      type="button"
      (click)="saveMappings()"
      [disabled]="!canSave"
      class="save-button"
    >
      <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
      <mat-icon *ngIf="!isSaving">save</mat-icon>
      {{ isSaving ? 'Saving...' : 'Save Mappings' }}
    </button>

    <!-- Temporary debug button -->
    <button
      mat-button
      type="button"
      (click)="debugLog()"
      style="margin-left: 10px;"
    >
      Debug State
    </button>
  </div>
</div>
