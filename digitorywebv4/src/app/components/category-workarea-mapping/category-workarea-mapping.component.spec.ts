import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { of } from 'rxjs';

import { CategoryWorkareaMappingComponent, CategoryWorkareaMapping, WorkAreaData } from './category-workarea-mapping.component';
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

describe('CategoryWorkareaMappingComponent', () => {
  let component: CategoryWorkareaMappingComponent;
  let fixture: ComponentFixture<CategoryWorkareaMappingComponent>;
  let mockSmartDashboardService: jasmine.SpyObj<SmartDashboardService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;

  const mockCategories = ['Food', 'Beverages', 'Tobacco', 'Liquor'];
  const mockWorkAreas: WorkAreaData[] = [
    {
      restaurantIdOld: 'rest1',
      branchName: 'Branch 1',
      workAreas: ['Kitchen', 'Bar'],
      disabled: false
    },
    {
      restaurantIdOld: 'rest2',
      branchName: 'Branch 2',
      workAreas: ['Kitchen', 'Bakery'],
      disabled: false
    }
  ];

  const mockExistingMappings: CategoryWorkareaMapping[] = [
    {
      categoryName: 'Food',
      workAreas: ['Kitchen']
    },
    {
      categoryName: 'Beverages',
      workAreas: ['Bar', 'Kitchen']
    }
  ];

  beforeEach(async () => {
    const smartDashboardServiceSpy = jasmine.createSpyObj('SmartDashboardService', ['getCategories']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);

    await TestBed.configureTestingModule({
      imports: [
        CategoryWorkareaMappingComponent,
        ReactiveFormsModule,
        NoopAnimationsModule,
        MatSnackBarModule
      ],
      providers: [
        { provide: SmartDashboardService, useValue: smartDashboardServiceSpy },
        { provide: AuthService, useValue: authServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CategoryWorkareaMappingComponent);
    component = fixture.componentInstance;
    mockSmartDashboardService = TestBed.inject(SmartDashboardService) as jasmine.SpyObj<SmartDashboardService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

    // Setup default mocks
    mockAuthService.getCurrentUser.and.returnValue({ tenantId: 'test-tenant' });
    mockSmartDashboardService.getCategories.and.returnValue(of({
      categories: mockCategories.map(name => ({ name }))
    }));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with input properties', () => {
    component.tenantId = 'test-tenant';
    component.categories = mockCategories;
    component.workAreas = mockWorkAreas;
    component.selectedCategoryNames = ['Food', 'Beverages'];
    component.existingMappings = mockExistingMappings;

    component.ngOnInit();

    expect(component.selectedCategories).toEqual(['Food', 'Beverages']);
    expect(component.allWorkAreas).toEqual(['Kitchen', 'Bar', 'Bakery']);
    expect(component.mappings).toEqual(mockExistingMappings);
  });

  it('should load categories when no input provided', () => {
    component.tenantId = 'test-tenant';
    component.ngOnInit();

    expect(mockSmartDashboardService.getCategories).toHaveBeenCalledWith('test-tenant');
    expect(component.categories).toEqual(mockCategories);
  });

  it('should extract all work areas from work area data', () => {
    component.workAreas = mockWorkAreas;
    component['extractAllWorkAreas']();

    expect(component.allWorkAreas).toEqual(['Kitchen', 'Bar', 'Bakery']);
  });

  it('should build form from selected categories', () => {
    component.categories = mockCategories;
    component.workAreas = mockWorkAreas;
    component.selectedCategoryNames = ['Food', 'Beverages'];
    component.existingMappings = mockExistingMappings;

    component.ngOnInit();

    expect(component.mappingsFormArray.length).toBe(2);
    expect(component.mappingsFormArray.at(0).get('categoryName')?.value).toBe('Food');
    expect(component.mappingsFormArray.at(1).get('categoryName')?.value).toBe('Beverages');
  });

  it('should handle work areas change', () => {
    component.categories = mockCategories;
    component.workAreas = mockWorkAreas;
    component.selectedCategoryNames = ['Food'];
    component.ngOnInit();

    const newWorkAreas = ['Kitchen', 'Bar'];
    component.onWorkAreasChange(0, newWorkAreas);

    expect(component.mappingsFormArray.at(0).get('workAreas')?.value).toEqual(newWorkAreas);
  });

  it('should validate mappings correctly', () => {
    component.categories = mockCategories;
    component.workAreas = mockWorkAreas;
    component.selectedCategoryNames = ['Food'];
    component.ngOnInit();

    // Set empty work areas to trigger validation error
    component.mappingsFormArray.at(0).patchValue({ workAreas: [] });
    component['validateMappings']();

    expect(component.validationErrors.length).toBeGreaterThan(0);
    expect(component.validationErrors[0]).toContain('must have at least one work area selected');
  });

  it('should get correct work areas placeholder', () => {
    component.categories = mockCategories;
    component.workAreas = mockWorkAreas;
    component.selectedCategoryNames = ['Food'];
    component.ngOnInit();

    // Test empty selection
    expect(component.getWorkAreasPlaceholder(0)).toBe('Select Work Areas');

    // Test single selection
    component.mappingsFormArray.at(0).patchValue({ workAreas: ['Kitchen'] });
    expect(component.getWorkAreasPlaceholder(0)).toBe('Kitchen');

    // Test multiple selections
    component.mappingsFormArray.at(0).patchValue({ workAreas: ['Kitchen', 'Bar'] });
    expect(component.getWorkAreasPlaceholder(0)).toBe('Kitchen, Bar');

    // Test many selections
    component.mappingsFormArray.at(0).patchValue({ workAreas: ['Kitchen', 'Bar', 'Bakery', 'Grill'] });
    expect(component.getWorkAreasPlaceholder(0)).toBe('4 work areas selected');
  });

  it('should emit mappings on save', (done) => {
    component.categories = mockCategories;
    component.workAreas = mockWorkAreas;
    component.selectedCategoryNames = ['Food'];
    component.ngOnInit();

    component.mappingsFormArray.at(0).patchValue({ workAreas: ['Kitchen'] });

    component.mappingsChanged.subscribe((mappings: CategoryWorkareaMapping[]) => {
      expect(mappings.length).toBe(1);
      expect(mappings[0].categoryName).toBe('Food');
      expect(mappings[0].workAreas).toEqual(['Kitchen']);
      done();
    });

    component.saveMappings();
  });

  it('should handle changes to selected categories', () => {
    component.categories = mockCategories;
    component.workAreas = mockWorkAreas;
    component.selectedCategoryNames = ['Food'];
    component.ngOnInit();

    expect(component.mappingsFormArray.length).toBe(1);

    // Change selected categories
    component.selectedCategoryNames = ['Food', 'Beverages'];
    component.ngOnChanges({
      selectedCategoryNames: {
        currentValue: ['Food', 'Beverages'],
        previousValue: ['Food'],
        firstChange: false,
        isFirstChange: () => false
      }
    });

    expect(component.mappingsFormArray.length).toBe(2);
  });

  it('should handle changes to work areas', () => {
    component.categories = mockCategories;
    component.workAreas = mockWorkAreas;
    component.selectedCategoryNames = ['Food'];
    component.ngOnInit();

    const originalWorkAreas = [...component.allWorkAreas];

    // Change work areas
    const newWorkAreas: WorkAreaData[] = [
      {
        restaurantIdOld: 'rest3',
        branchName: 'Branch 3',
        workAreas: ['Kitchen', 'Grill'],
        disabled: false
      }
    ];

    component.workAreas = newWorkAreas;
    component.ngOnChanges({
      workAreas: {
        currentValue: newWorkAreas,
        previousValue: mockWorkAreas,
        firstChange: false,
        isFirstChange: () => false
      }
    });

    expect(component.allWorkAreas).toEqual(['Kitchen', 'Grill']);
    expect(component.allWorkAreas).not.toEqual(originalWorkAreas);
  });
});
