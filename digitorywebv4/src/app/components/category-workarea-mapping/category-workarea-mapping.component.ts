import { Component, OnInit, OnDestroy, OnChanges, SimpleChanges, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Material modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';

// Services
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

export interface CategoryWorkareaMapping {
  categoryName: string;
  workAreas: string[];
}

export interface WorkAreaData {
  restaurantIdOld: string;
  branchName: string;
  workAreas: string[];
  disabled: boolean;
}

@Component({
  selector: 'app-category-workarea-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule
  ],
  templateUrl: './category-workarea-mapping.component.html',
  styleUrls: ['./category-workarea-mapping.component.scss']
})
export class CategoryWorkareaMappingComponent implements OnInit, OnChanges, OnDestroy {
  @Input() tenantId: string = '';
  @Input() showAsDialog: boolean = false;
  @Input() categories: string[] = [];
  @Input() selectedCategoryNames: string[] = [];
  @Input() workAreas: WorkAreaData[] = [];
  @Input() existingMappings: CategoryWorkareaMapping[] = [];
  @Output() mappingsChanged = new EventEmitter<CategoryWorkareaMapping[]>();
  @Output() closeDialog = new EventEmitter<void>();

  // Form and data
  mappingForm: FormGroup;
  mappings: CategoryWorkareaMapping[] = [];
  selectedCategories: string[] = [];
  allWorkAreas: string[] = [];

  // UI state
  isLoading = false;
  isSaving = false;
  validationErrors: string[] = [];
  
  // Destroy subject for cleanup
  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    if (!this.tenantId) {
      const user = this.authService.getCurrentUser();
      this.tenantId = user?.tenantId || '';
    }

    // Use input properties if provided, otherwise load data
    if (this.categories.length > 0 && this.workAreas.length > 0) {
      this.updateSelectedCategories();
      this.extractAllWorkAreas();
      this.mappings = this.existingMappings || [];
      this.buildFormFromMappings();
    } else if (this.tenantId) {
      this.loadData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedCategoryNames'] && !changes['selectedCategoryNames'].firstChange) {
      this.updateSelectedCategories();
      this.buildFormFromSelectedCategories();
    }
    
    if (changes['workAreas'] && !changes['workAreas'].firstChange) {
      this.extractAllWorkAreas();
      this.buildFormFromSelectedCategories();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ===== INITIALIZATION =====
  private initializeForm(): void {
    this.mappingForm = this.fb.group({
      mappings: this.fb.array([])
    });
  }

  get mappingsFormArray(): FormArray {
    return this.mappingForm.get('mappings') as FormArray;
  }

  // ===== DATA LOADING =====
  private loadData(): void {
    this.isLoading = true;
    this.loadCategories();
    // Work areas should be provided as input from parent component
  }

  private loadCategories(): void {
    this.smartDashboardService.getCategories(this.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response && response.categories) {
            this.categories = response.categories.map((cat: any) => cat.name || cat.categoryName);
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showError('Failed to load categories');
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // ===== WORK AREAS MANAGEMENT =====
  private extractAllWorkAreas(): void {
    this.allWorkAreas = [];
    this.workAreas.forEach(workAreaData => {
      if (workAreaData.workAreas && workAreaData.workAreas.length > 0) {
        this.allWorkAreas.push(...workAreaData.workAreas);
      }
    });
    // Remove duplicates
    this.allWorkAreas = [...new Set(this.allWorkAreas)];
  }

  // ===== FORM MANAGEMENT =====
  private buildFormFromMappings(): void {
    // If we have selected categories, use them
    if (this.selectedCategoryNames.length > 0) {
      this.buildFormFromSelectedCategories();
      return;
    }

    // If no categories are selected, clear the form array (empty right side)
    const mappingsArray = this.mappingsFormArray;
    mappingsArray.clear();
    this.cdr.detectChanges();
  }

  private createMappingFormGroup(mapping: CategoryWorkareaMapping): FormGroup {
    return this.fb.group({
      categoryName: [mapping.categoryName, Validators.required],
      workAreas: [mapping.workAreas || []]
    });
  }

  // ===== SELECTED CATEGORIES MANAGEMENT =====
  private updateSelectedCategories(): void {
    this.selectedCategories = this.categories.filter(category =>
      this.selectedCategoryNames.includes(category)
    );
  }

  private buildFormFromSelectedCategories(): void {
    // Store current form values to preserve user selections
    const currentFormValues = new Map<string, string[]>();
    for (let i = 0; i < this.mappingsFormArray.length; i++) {
      const formGroup = this.mappingsFormArray.at(i) as FormGroup;
      const categoryName = formGroup.get('categoryName')?.value;
      const workAreas = formGroup.get('workAreas')?.value || [];
      if (categoryName) {
        currentFormValues.set(categoryName, workAreas);
      }
    }

    // Get currently selected category names for comparison
    const currentCategoryNames = new Set(
      Array.from({ length: this.mappingsFormArray.length }, (_, i) => {
        const formGroup = this.mappingsFormArray.at(i) as FormGroup;
        return formGroup.get('categoryName')?.value;
      }).filter(Boolean)
    );

    const newCategoryNames = new Set(this.selectedCategories);

    // Remove categories that are no longer selected
    for (let i = this.mappingsFormArray.length - 1; i >= 0; i--) {
      const formGroup = this.mappingsFormArray.at(i) as FormGroup;
      const categoryName = formGroup.get('categoryName')?.value;
      if (!newCategoryNames.has(categoryName)) {
        this.mappingsFormArray.removeAt(i);
      }
    }

    // Add new categories that were just selected
    this.selectedCategories.forEach(categoryName => {
      if (!currentCategoryNames.has(categoryName)) {
        // Find existing mapping for this category
        const existingMapping = this.mappings.find(m => m.categoryName === categoryName);

        // Check if we have current form values for this category (from previous selections)
        const preservedWorkAreas = currentFormValues.get(categoryName) || [];

        const mapping: CategoryWorkareaMapping = existingMapping || {
          categoryName: categoryName,
          workAreas: preservedWorkAreas // Preserve user selections or start empty
        };

        // If we have preserved work areas, use them instead of existing mapping
        if (preservedWorkAreas.length > 0) {
          mapping.workAreas = preservedWorkAreas;
        }

        this.mappingsFormArray.push(this.createMappingFormGroup(mapping));
      }
    });

    // Sort form array to match the order of selected categories
    const sortedControls: FormGroup[] = [];
    this.selectedCategories.forEach(categoryName => {
      const control = this.mappingsFormArray.controls.find(ctrl => {
        const formGroup = ctrl as FormGroup;
        return formGroup.get('categoryName')?.value === categoryName;
      }) as FormGroup;
      if (control) {
        sortedControls.push(control);
      }
    });

    // Clear and rebuild with sorted controls
    while (this.mappingsFormArray.length !== 0) {
      this.mappingsFormArray.removeAt(0);
    }
    sortedControls.forEach(control => {
      this.mappingsFormArray.push(control);
    });

    // Trigger change detection
    this.cdr.detectChanges();
  }

  // ===== FORM ACTIONS =====
  onWorkAreasChange(index: number, workAreas: string[]): void {
    const mappingGroup = this.mappingsFormArray.at(index) as FormGroup;
    if (mappingGroup) {
      mappingGroup.patchValue({ workAreas });

      // Update the internal mappings array to keep it in sync
      const categoryName = mappingGroup.get('categoryName')?.value;
      if (categoryName) {
        const existingMappingIndex = this.mappings.findIndex(m => m.categoryName === categoryName);
        if (existingMappingIndex >= 0) {
          this.mappings[existingMappingIndex].workAreas = workAreas;
        } else {
          // Add new mapping if it doesn't exist
          this.mappings.push({
            categoryName,
            workAreas
          });
        }
      }

      this.cdr.detectChanges();
      this.validateMappings();
    }
  }

  // ===== VALIDATION =====
  private validateMappings(): void {
    this.validationErrors = [];
    const formMappings = this.mappingsFormArray.value as CategoryWorkareaMapping[];

    // Basic validation - ensure each category has at least one work area
    formMappings.forEach(mapping => {
      if (!mapping.workAreas || mapping.workAreas.length === 0) {
        this.validationErrors.push(`Category "${mapping.categoryName}" must have at least one work area selected`);
      }
    });
  }

  // ===== SAVE FUNCTIONALITY =====
  saveMappings(): void {
    if (this.mappingForm.invalid) {
      this.showError('Please fix form errors before saving');
      return;
    }

    this.validateMappings();
    if (this.validationErrors.length > 0) {
      this.showError('Please fix validation errors before saving');
      return;
    }

    this.isSaving = true;
    const mappings = this.mappingsFormArray.value as CategoryWorkareaMapping[];
    
    // Filter out empty mappings
    const validMappings = mappings.filter(m => 
      m.categoryName && m.workAreas.length > 0
    );

    // For now, just emit the mappings - actual save implementation depends on backend API
    setTimeout(() => {
      this.mappings = validMappings;
      this.mappingsChanged.emit(validMappings);
      this.showSuccess('Mappings saved successfully');
      this.isSaving = false;
      this.cdr.detectChanges();
    }, 1000);
  }

  // ===== UTILITY METHODS =====
  getWorkAreasPlaceholder(index: number): string {
    if (index >= this.mappingsFormArray.length) {
      return 'Select Work Areas';
    }

    const mappingGroup = this.mappingsFormArray.at(index) as FormGroup;
    if (!mappingGroup) {
      return 'Select Work Areas';
    }

    const selectedWorkAreas = mappingGroup.get('workAreas')?.value || [];

    if (selectedWorkAreas.length === 0) {
      return 'Select Work Areas';
    } else if (selectedWorkAreas.length === 1) {
      return selectedWorkAreas[0];
    } else if (selectedWorkAreas.length <= 3) {
      return selectedWorkAreas.join(', ');
    } else {
      return `${selectedWorkAreas.length} work areas selected`;
    }
  }

  // ===== UI HELPERS =====
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  onClose(): void {
    this.closeDialog.emit();
  }

  // ===== GETTERS FOR TEMPLATE =====
  get hasValidationErrors(): boolean {
    return this.validationErrors.length > 0;
  }

  get canSave(): boolean {
    return !this.isSaving &&
           this.mappingForm.valid &&
           this.validationErrors.length === 0 &&
           this.mappingsFormArray.length > 0;
  }

  // ===== TRACK BY FUNCTIONS =====
  trackByCategoryName(index: number, item: any): string {
    const formGroup = item as FormGroup;
    return formGroup.get('categoryName')?.value || index.toString();
  }

  trackByWorkArea(_index: number, item: string): string {
    return item;
  }
}
